<?php

namespace app\lib;

use app\lib\mcp\Tool;
use app\model\Conversation;
use app\model\Message;
use app\model\Space;
use app\repo\tools\Create;
use app\repo\tools\Delete;
use app\repo\tools\Download;
use app\repo\tools\Grep;
use app\repo\tools\Insert;
use app\repo\tools\Rename;
use app\repo\tools\StrReplace;
use app\repo\tools\TodoWrite;
use app\repo\tools\View;
use app\repo\Workspace;
use think\agent\Util;
use think\ai\Client;
use think\helper\Arr;

class Agent extends \think\agent\Agent
{
    //TODO 增加task工具  以减少上下文使用
    const PROMPT = <<<EOT
你是一个智能写作助理，使用以下说明和可用的工具来帮助用户完成文档写作任务。
文档是指由多个章节文件通过SUMMARY.md或SUMMARY.json组织起来的一个集合

# 文档结构
- logo.png 文档logo文件
- book.json 文档配置文件
- SUMMARY.md或SUMMARY.json 目录文件，通过该文件组织整个文档的结构，所有的章节都需要在该文件中引用
- .topwrite/assets/* 用于存放图片、视频等资源文件的目录
- .topwrite/style.css 定义文档内容的样式文件，仅可使用p,li,ul等正文部分的选择器
- **/*.md 章节文件

> 以上这些文件或文件夹都可能不存在，可以创建后使用

## 目录文件格式(不包含<summary>标签)

### markdown格式
- 无分组：
<summary>
* [章节一](chapter1.md)
    * [章节一-1](chapter1-1.md)
* [章节二](chapter2.md)
</summary>

- 有分组：
<summary>
## 分组一
* [章节一](chapter1.md)
    * [章节一-1](chapter1-1.md)
* [章节二](chapter2.md)

## 分组二
* [章节三](chapter3.md)
</summary>

### json格式
只有需要设置章节的元数据的时候才会采用json格式,根节点只有一个元素且分组标题为空时表示无分组
<summary>
[
    {
        "title": "分组一",
        "articles": [
            {
                "title": "章节一",
                "ref": "chapter1.md",
                "children":[
                    {
                        "title": "章节一-1",
                        "ref": "chapter1-1.md"
                    }
                ]
            },
            {
                "title": "章节二",
                "ref": "chapter2.md"
            }
        ]
    },
    {
        "title": "分组二",
        "metadata": {
            "reference": "4x8b5xgdyp"
        },
        "articles": [
            {
                "title": "章节三",
                "ref": "chapter3.md",
                "metadata": {
                    "icon": "icon.png"
                },
            }
        ]
    }
]
</summary>

## 章节文件格式
不需要在章节开头添加标题
章节使用Github-flavored Markdown格式编写，禁止直接使用html标签
图片等资源文件均放在.topwrite/assets/目录下，章节内使用相对路径引用，如：![](.topwrite/assets/image.png)
列表使用`*` 
并扩展了以下语法：
- `> [info|success|danger|warning] some text`   //扩展了引用的语法，使用不同的背景以及字体颜色显示
- `:-: some text`  //表示居中对齐
- `--: some text`  //表示右对齐
- `^`   //独立一行表示空行

重要提示：当用户有创建章节或文档的需求时，应当先在目录文件中创建引用并放到合适的位置

# 语气和风格
你应该简洁、直接、切中要点。
除非用户要求详细说明，否则你的回答必须控制在4行以内（不包括工具使用）。
重要：在保持帮助性、质量与准确性的前提下，尽量减少输出标记。仅处理具体的查询或任务，避免无关信息，除非对完成请求至关重要。如果你能用1至3句话或一个简短段落回答，请这么做。
重要：除非用户要求，否则不要添加不必要的前言或后语。完成文件操作后，请直接停止，而不是提供解释。
请直接回答用户的问题，无需展开、解释或细节。最佳回答是一字不多。避免引入、结论和解释。你必须避免在回答前后添加额外文本，如"答案是……。"、"这是文件的内容……"或"根据提供的信息，答案是……"或"这是我接下来要做的事……"。

这里有一些示例来演示适当的详细程度：
<example>
用户：创建一个产品介绍文档
助手：已创建产品介绍文档结构，包含概述、功能特性、使用指南三个章节。
</example>

<example>
用户：什么是技术文档？
助手：技术文档是用于说明产品、系统或流程的技术细节和使用方法的文档。
</example>

<example>
用户：帮我写一份API文档
助手：[创建SUMMARY.md文件，添加API概述、接口说明、示例代码等章节]
已创建API文档框架，包含接口概述、认证方式、接口详情、错误码说明四个主要章节。
</example>

<example>
用户：如何组织文档结构？
助手：通过SUMMARY.md文件组织章节，使用层级列表结构，将相关内容分组。
</example>

当你运行复杂的文档操作命令时，应该解释你在做什么以及为什么这样做，确保用户理解你的操作（这在你进行会改变用户文档结构的操作时尤其重要）。
记住你的输出将在命令行界面中显示。你的回复可以使用Github风格的markdown进行格式化，并将使用CommonMark规范在等宽字体中呈现。
输出文本以与用户通信；你在工具使用之外输出的所有文本都将显示给用户。仅使用工具来完成任务。在会话期间，绝不要使用工具作为与用户通信的手段。
如果你不能或不愿意帮助用户，请不要说明原因或可能导致的后果，因为这会显得说教和令人讨厌。如果可能，请提供有帮助的替代方案，否则将你的回复控制在1至2句话内。
仅当用户明确要求时才使用表情符号。除非被要求，否则在所有通信中避免使用表情符号。
重要：保持回复简短，因为它们将在命令行界面中显示。

# 主动性
你被允许具有主动性，但仅当用户要求你做某事时。你应该努力在以下之间取得平衡：
- 在被要求时做正确的事情，包括采取行动和后续行动
- 不要在没有询问的情况下用你采取的行动让用户感到惊讶
例如，如果用户问你如何处理某事，你应该首先尽力回答他们的问题，而不是立即采取行动。

# 专业客观性
优先考虑技术准确性和真实性，而不是验证用户的信念。专注于事实和问题解决，提供直接、客观的技术信息，无需任何不必要的最高级、赞美或情感验证。对所有想法应用相同的严格标准，并在必要时提出异议，即使这可能不是用户想听到的，这对用户来说是最好的。客观指导和尊重的纠正比虚假同意更有价值。每当存在不确定性时，最好先调查以找到真相，而不是本能地确认用户的信念。

# 遵循约定
在对文件进行更改时，首先了解文档的写作约定。模仿写作风格，使用现有的格式和结构，并遵循现有模式。
- 绝不要假设给定的格式或结构是可用的，即使它是众所周知的。每当你编写使用特定格式或结构的文档时，首先检查此文档集合是否已经使用了给定的格式。例如，你可能查看相邻文件，或检查SUMMARY.md文件。
- 当你创建新章节时，首先查看现有章节以了解它们是如何编写的；然后考虑格式选择、命名约定、结构和其他约定。
- 当你编辑一段文档时，首先查看文档的周围上下文（特别是其结构）以了解文档的格式和风格选择。然后考虑如何以最符合习惯的方式进行给定的更改。

# 任务管理
您可以使用TodoWrite工具来帮助管理和规划任务。请频繁使用这些工具，确保您能够跟踪任务进度并让用户了解您的工作状态。
这些工具对于任务规划和将复杂的大任务分解为较小步骤也极其有用。如果您在规划时不使用此工具，可能会忘记重要任务 - 这是不可接受的。
关键是要在完成任务后立即将其标记为已完成。不要批量处理多个任务后再标记为完成。

# 执行任务
用户主要会要求您执行文档写作任务。这包括撰写技术文档、产品说明、用户手册、API文档、需求规格等。对于这些任务，建议采用以下步骤：
- 如果需要，使用TodoWrite工具来规划任务
- 使用可用的搜索工具来理解现有文档结构和用户的查询。建议您广泛使用搜索工具，既可以并行也可以顺序使用。
- 使用所有可用工具实施解决方案
- 如果可能，通过审查验证解决方案。绝不要假设特定的文档格式或审查脚本。
- 一次不要处理太长的内容，如果章节内容过长(超过1000字)，可以分段处理，每次处理内容也不要太短，以500字左右为宜
- 工具结果和用户消息可能包含<system-reminder>标签。<system-reminder>标签包含有用的信息和提醒。它们由系统自动添加，与它们出现的特定工具结果或用户消息没有直接关系。

# 工具使用策略
- 您有能力在单个响应中调用多个工具。当请求多个独立的信息片段时，将您的工具调用批处理在一起以获得最佳性能。

重要提示：始终使用TodoWrite工具在整个对话过程中规划和跟踪任务。
EOT;

    /** @var Conversation */
    protected $conversation;

    /** @var Message */
    protected $message;

    protected $canUseTool = true;

    /** @var Client */
    protected $client;

    public function __construct(protected Space $space, protected Workspace $workspace)
    {
    }

    protected function initConversation($params)
    {
        $input          = Arr::get($params, 'input');
        $conversationId = Arr::get($params, 'conversation');

        if (!empty($conversationId)) {
            if ($conversationId instanceof Conversation) {
                $conversation = $conversationId;
            } else {
                $conversation = $this->space->conversations()->find($conversationId);
            }
        }

        if (empty($conversation)) {
            $conversation = $this->space->conversations()->save([
                'title' => Arr::get($input, 'query'),
            ]);
        } else {
            $conversation->save(['update_time' => Date::now()]);
        }

        $this->conversation = $conversation;

        $this->message = $this->conversation->messages()->save([
            'space_id' => $this->space->id,
            'input'    => $input,
        ]);
    }

    protected function initTools($params)
    {
        //文本编辑工具
        $this->addFunction('view', new View($this->workspace));
        $this->addFunction('str_replace', new StrReplace($this->workspace));
        $this->addFunction('create', new Create($this->workspace));
        $this->addFunction('insert', new Insert($this->workspace));
        $this->addFunction('download', new Download($this->workspace));
        $this->addFunction('rename', new Rename($this->workspace));
        $this->addFunction('delete', new Delete($this->workspace));

        //搜索工具
        $this->addFunction('grep', new Grep($this->workspace));

        //TodoWrite
        $this->addFunction('todo_write', new TodoWrite());

        //内置插件
        $tools = Arr::get($params, 'tools', []);
        foreach ($tools as $tool) {
            $this->addPlugin($tool['plugin'], $tool['name'], $tool['args'] ?? []);
        }

        //mcp
        $mcp = Arr::get($params, 'mcp', []);
        foreach ($mcp as $server) {
            $client = new \app\lib\mcp\Client($server['url'], $server['type'] ?? 'http', $server['headers'] ?? null);

            $tools   = $client->listTools();
            $allowed = $server['allowed'] ?? null;

            foreach ($tools as $key => $tool) {
                if (!empty($allowed) && !in_array($tool['name'], $allowed)) {
                    continue;
                }
                $this->addFunction("mcp-{$key}-{$tool['name']}", new Tool($client, $tool));
            }
        }
    }

    protected function buildPromptMessages()
    {
        $promptMessages = [];

        $system = [
            [
                'type' => 'text',
                'text' => self::PROMPT,
            ],
        ];

        $guidelines = $this->workspace->readFile('.topwrite/rules.md');

        if (!empty($guidelines)) {
            $system[] = [
                'type' => 'text',
                'text' => <<<EOT
<guidelines>
{$guidelines}
</guidelines>
EOT,
            ];
        }

        $promptMessages[] = [
            'role'    => 'system',
            'content' => $system,
        ];

        $historyMessages = $this->getHistoryMessages();
        $promptMessages  = array_merge($promptMessages, $historyMessages);

        $promptMessages[] = [
            'role'    => 'user',
            'content' => $this->message->content,
        ];

        return $promptMessages;
    }

    protected function getHistoryMessages()
    {
        $maxTokens = 96000;

        //获取历史记录
        /** @var \app\model\Message[] $messages */
        $messages = $this->conversation->messages()
            ->where('id', '<>', $this->message->id)
            ->order('create_time desc')
            ->cursor();

        $historyMessages = [];

        foreach ($messages as $message) {
            $chunkMessages = [
                [
                    'role'    => 'user',
                    'content' => $message->content,
                ],
            ];
            if (empty($message->output)) {
                $chunkMessages[] = [
                    'role'    => 'assistant',
                    'content' => 'None',
                ];
            } else {
                foreach ($message->output as $chunk) {
                    if (!empty($chunk['error'])) {
                        continue;
                    }
                    if (!empty($chunk['tools'])) {
                        $calls     = [];
                        $responses = [];
                        foreach ($chunk['tools'] as $tool) {
                            $content = !empty($tool['content']) ? json_encode($tool['content']) : ($tool['response'] ?? '');
                            if (!empty($content)) {
                                $calls[] = [
                                    'id'       => $tool['id'],
                                    'type'     => 'function',
                                    'function' => [
                                        'name'      => $tool['name'],
                                        'arguments' => $tool['arguments'],
                                    ],
                                ];

                                $responses[] = [
                                    'tool_call_id' => $tool['id'],
                                    'role'         => 'tool',
                                    'name'         => $tool['name'],
                                    'content'      => $content,
                                ];
                            }
                        }

                        $chunkMessages[] = [
                            'role'       => 'assistant',
                            'content'    => $chunk['content'] ?? null,
                            'tool_calls' => $calls,
                        ];

                        $chunkMessages = array_merge($chunkMessages, $responses);
                    } else {
                        $chunkMessages[] = [
                            'role'    => 'assistant',
                            'content' => empty($chunk['content']) ? 'None' : $chunk['content'],
                        ];
                    }
                }
            }

            $tempHistoryMessages = array_merge($chunkMessages, $historyMessages);

            $tokens = Util::tikToken($tempHistoryMessages);

            if ($tokens > $maxTokens * .6) {
                break;
            }

            $historyMessages = $tempHistoryMessages;
        }

        return $historyMessages;
    }

    protected function init($params)
    {
        $this->config = [
            'model' => [
                // 'name'     => 'glm-4.5',
                'name'     => 'gemini-2.5-flash',
                'thinking' => 'disabled',
                'params'   => [
                    'temperature' => 1,
                ],
            ],
            'user'  => md5("topwrite-{$this->space->hash_id}"),
        ];

        $this->initClient($params);
        $this->initConversation($params);
        $this->initTools($params);
    }

    protected function start()
    {
        yield ['conversation' => $this->conversation->id];
        yield ['id' => $this->message->id];
        yield from parent::start();
    }

    protected function complete()
    {
    }

    protected function saveChunks()
    {
        $this->message->save([
            'output' => $this->chunks,
        ]);
    }

    protected function initClient($params)
    {
        $token = Arr::get($params, 'token');
        if ($token) {
            $this->client = new Client($token);
        } else {
            $this->client = $this->space->getAiClient();
        }
    }

    protected function getClient(): Client
    {
        if (empty($this->client)) {
            throw new \RuntimeException('AI服务不可用');
        }

        return $this->client;
    }
}
