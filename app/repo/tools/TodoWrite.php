<?php

namespace app\repo\tools;

use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Error;

class TodoWrite extends FunctionCall
{
    protected $title = 'TodoWrite';

    protected $name = 'todo_write';

    protected $description = <<<EOT
使用此工具为您当前的文档写作项目创建和管理结构化任务列表。这有助于您跟踪写作进度、组织复杂的文档结构，并向用户展示您的专业性。
它还帮助用户了解文档创作的进度和他们请求的整体完成情况。

## 何时使用此工具
在以下文档写作场景中主动使用此工具：
1. 复杂的多章节文档 - 当文档需要3个或更多不同的章节或部分时
2. 大型文档项目 - 需要仔细规划结构、研究内容或多个写作阶段的任务
3. 用户明确请求写作计划 - 当用户直接要求您制定写作计划或大纲时
4. 用户提供多个写作任务 - 当用户提供要完成的文档列表时（编号或逗号分隔）
5. 收到新写作需求后 - 立即将用户的文档需求捕获为待办事项
6. 开始写作任务时 - 在开始写作之前将其标记为进行中。理想情况下，您应该一次只有一个写作任务处于进行中状态
7. 完成写作任务后 - 将其标记为已完成，并添加在写作过程中发现的任何新后续任务（如校对、格式调整等）

## 何时不使用此工具
在以下情况下跳过使用此工具：
1. 只有一个简单的写作任务（如单个段落或简短回复）
2. 任务是琐碎的，跟踪它不会提供组织上的好处
3. 写作任务可以在少于3个简单步骤中完成
4. 任务纯粹是对话性或咨询性的（如回答简单问题）

注意：如果只有一个简单的写作任务要做，您不应该使用此工具。在这种情况下，您最好直接完成写作。

## 使用待办事项列表的示例

<example>
用户：我需要写一份完整的产品使用手册，包括安装指南、功能介绍和故障排除。完成后请检查格式和内容完整性！
助手：我将帮助您创建一份完整的产品使用手册。让我创建一个待办事项列表来跟踪此写作项目。
*创建包含以下项目的待办事项列表：*
1. 制定手册整体结构和目录大纲
2. 编写产品安装指南章节
3. 撰写功能介绍和操作说明章节
4. 创建故障排除和常见问题章节
5. 检查文档格式、语言表达和内容完整性
*开始处理第一个任务*

<reasoning>
助手使用待办事项列表是因为：
1. 产品使用手册是一个多章节文档，需要结构规划、内容写作和格式调整
2. 用户明确要求之后检查格式和内容完整性
3. 助手推断需要系统性地完成每个章节，因此添加了"检查文档质量"作为最终任务
</reasoning>
</example>

<example>
用户：帮我将现有的技术文档从英文翻译成中文，并保持专业术语的一致性
助手：让我首先查看您的技术文档以了解内容结构和专业术语。
*审查文档结构、章节划分和关键术语*
助手：我发现文档包含8个主要章节和大量专业术语。让我创建一个待办事项列表来跟踪翻译工作。
*创建包含每个章节翻译和术语统一的特定项目的待办事项列表*

<reasoning>
助手使用待办事项列表是因为：
1. 首先，助手分析文档以了解翻译任务的范围
2. 发现多个章节和专业术语后，确定这是一个需要系统处理的复杂任务
3. 待办事项列表有助于确保每个章节都被准确翻译并保持术语一致性
4. 这种方法防止遗漏任何内容并确保翻译质量
</reasoning>
</example>


<example>
用户：我需要为我的公司创建这些文档：员工手册、培训材料、操作流程和安全规范。
助手：我将帮助创建这些企业文档。首先，让我们将所有文档需求添加到待办事项列表中。
*创建一个待办事项列表，根据企业需求将每个文档分解为特定的写作任务*
助手：让我们从员工手册开始。这将涉及制定公司政策、福利说明和行为准则等核心内容。

<reasoning>
助手使用待办事项列表是因为：
1. 用户提供了多个复杂文档以逗号分隔列表的形式创建
2. 待办事项列表有助于将这些大型文档项目组织成可管理的写作任务
3. 这种方法允许跟踪整个文档创建过程中的进度
</reasoning>
</example>

<example>
用户：您能帮助改进我的技术博客文章吗？它的结构混乱，读者反馈说难以理解。
助手：我将帮助改进您的技术博客文章。首先，让我分析现有内容以识别结构和表达问题。
*审查文章结构、逻辑流程、语言表达和技术准确性*
助手：分析您的文章后，我发现了几个需要改进的地方。让我创建一个待办事项列表来跟踪我们的优化工作。
*创建包含以下项目的待办事项列表：1）重新组织文章结构和逻辑流程，2）简化技术术语的解释，3）添加代码示例和图表说明，4）改进段落间的过渡和连接，5）校对语法和技术准确性*
让我们从重新组织文章结构和逻辑流程开始。

<reasoning>
助手使用待办事项列表是因为：
1. 首先，助手分析现有文章以识别具体的问题
2. 基于此分析，它识别了多个改进机会
3. 文章改进是一个需要多个步骤的复杂任务
4. 待办事项列表有助于系统地跟踪不同方面的改进
5. 这种系统方法确保所有问题都得到解决
</reasoning>
</example>

## 何时不使用待办事项列表的示例

<example>
用户：如何写一个简短的感谢邮件？
助手：写一个简短的感谢邮件可以按照以下结构：

主题：感谢您的[具体事项]

尊敬的[姓名]，

感谢您[具体的帮助或支持]。您的[行为/建议/帮助]对我们非常有价值。

再次表示诚挚的谢意。

此致
敬礼

[您的姓名]

<reasoning>
助手没有使用待办事项列表，因为这是一个可以在一个步骤中完成的单一、简单写作任务。对于如此简短的文档，没有必要跟踪多个任务或步骤。
</reasoning>
</example>

<example>
用户：什么是技术写作的基本原则？
助手：技术写作的基本原则包括：清晰性（使用简洁明了的语言）、准确性（确保技术信息正确）、完整性（提供必要的所有信息）、一致性（保持术语和格式统一）、以及用户导向（考虑读者的需求和背景）。

<reasoning>
助手没有使用待办事项列表，因为这是一个信息性咨询，没有实际的写作任务要完成。用户只是要求解释概念，而不是要求助手执行多个写作步骤或任务。
</reasoning>
</example>

<example>
用户：您能为这个段落添加一个小标题吗？
助手：当然，让我为这个段落添加一个合适的小标题。
* 分析段落内容并添加相关的小标题 *

<reasoning>
助手没有使用待办事项列表，因为这是一个局限于文档中一个位置的单一、直接任务。添加小标题不需要跟踪多个步骤或系统组织。
</reasoning>
</example>

<example>
用户：帮我检查这个句子的语法错误。
助手：我将检查您提供的句子的语法错误。

*审查句子语法结构*

我发现了以下语法问题并提供修正建议：
[具体的语法错误和修正建议]

<reasoning>
助手没有使用待办事项列表，因为这是一个具有即时结果的单一校对任务。没有多个步骤需要跟踪或组织，使得待办事项列表对于这个简单任务是不必要的。
</reasoning>
</example>

## 任务状态和管理

1. **任务状态**：使用这些状态来跟踪写作进度：
   - pending：写作任务尚未开始
   - in_progress：当前正在写作（一次限制一个任务）
   - completed：写作任务成功完成

2. **任务管理**：
   - 在写作时实时更新任务状态
   - 完成后立即标记任务完成（不要批量完成）
   - 任何时候必须恰好有一个任务处于in_progress状态（不多不少）
   - 在开始新写作任务之前完成当前任务
   - 完全从列表中删除不再相关的写作任务

3. **任务完成要求**：
   - 只有在完全完成写作任务时才标记为已完成
   - 如果遇到写作障碍、需要更多信息或无法完成，保持任务为in_progress状态
   - 当被阻塞时，创建一个新任务描述需要解决的问题
   - 在以下情况下永远不要标记任务为已完成：
     - 内容不完整或有明显错误
     - 格式不符合要求
     - 遇到未解决的写作问题
     - 缺少必要的信息或资料

4. **任务分解**：
   - 创建具体的、可操作的写作项目
   - 将复杂文档分解为更小的、可管理的章节或部分
   - 使用清晰、描述性的任务名称

如有疑问，请使用此工具。主动进行任务管理展示了专业性，并确保您成功完成所有写作要求。
EOT;

    protected $parameters = [
        'todos' => [
            'type'        => 'array',
            'description' => '待办事项列表',
            'items'       => [
                'type'       => 'object',
                'properties' => [
                    'content' => [
                        'type'        => 'string',
                        'description' => '待办事项内容',
                    ],
                    "status"  => [
                        "type"        => "string",
                        "description" => "待办事项状态",
                        "enum"        => ["pending", "in_progress", "completed"],
                    ],
                ],
                "required"   => ['content', 'status'],
            ],
        ],
    ];

    public function __construct()
    {
    }

    protected function run(Args $args)
    {
        $todos = $args->get('todos', []);

        // 验证待办事项数据
        foreach ($todos as $index => $todo) {
            if (empty(trim($todo['content'] ?? null))) {
                return new Error("第 " . ($index + 1) . " 个待办事项的内容不能为空");
            }

            if (!in_array($todo['status'] ?? null, ['pending', 'in_progress', 'completed'])) {
                return new Error("第 " . ($index + 1) . " 个待办事项的状态无效，必须是 pending、in_progress 或 completed");
            }
        }

        // 检查是否有多个进行中的任务
        $inProgressCount = 0;
        $completedCount  = 0;
        foreach ($todos as $todo) {
            if ($todo['status'] === 'in_progress') {
                $inProgressCount++;
            }
            if ($todo['status'] === 'completed') {
                $completedCount++;
            }
        }

        if ($inProgressCount > 1) {
            return new Error("同时只能有一个任务处于进行中状态，当前有 {$inProgressCount} 个任务处于进行中");
        }

        if ($completedCount == count($todos)) {
            return <<<EOT
Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable
<system-reminder>
This is a reminder that your todo list is currently empty. DO NOT mention this to the user explicitly because they are already aware. If you are working on tasks that would benefit from a todo list please use the TodoWrite tool to create one. If not, please feel free to ignore. Again do not mention this message to the user.
</system-reminder>
EOT;
        }

        $todos = json_encode($todos);

        return <<<EOT
Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable

<system-reminder>
Your todo list has changed. DO NOT mention this explicitly to the user. Here are the latest contents of your todo list:

{$todos}. Continue on with the tasks at hand if applicable.
</system-reminder>
EOT;
    }
}
